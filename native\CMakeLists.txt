cmake_minimum_required(VERSION 3.16)
project(dataset_export CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# ----------------- 1. 查找并配置依赖 -----------------
if(MINGW)
    message(STATUS "MinGW environment detected. Searching for dependencies in system paths.")
endif()

set(JAVA_HOME "C:/java/jdk-21.0.2")
find_package(JNI REQUIRED)
if(JNI_FOUND)
    message(STATUS "✓ Found JNI: headers at ${JNI_INCLUDE_DIRS}")
endif()

message(STATUS "Looking for DYNAMIC OpenCV...")
find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
if(OpenCV_FOUND)
    message(STATUS "✓ Found OpenCV ${OpenCV_VERSION} (dynamic)")
endif()

# ----------------- 2. 构建目标 -----------------
include_directories(src include ${JNI_INCLUDE_DIRS} ${OpenCV_INCLUDE_DIRS})
set(SOURCES src/dataset_export_jni.cpp src/coco_exporter.cpp src/voc_exporter.cpp src/yolo_exporter.cpp src/image_processor.cpp src/image_crop.cpp src/minio_client.cpp src/utils.cpp)
add_library(dataset_export SHARED ${SOURCES})

# ----------------- 3. 静态化MinGW运行时 (关键修改) -----------------
if(MINGW)
    message(STATUS "✓ Applying static linking options for MinGW runtime...")

    # 静态链接 C++ 和 C 运行时库
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libgcc -static-libstdc++")
    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -static-libgcc -static-libstdc++")

    # 将所有库链接项放在一个命令中，以精确控制顺序
    target_link_libraries(dataset_export PRIVATE
        ${OpenCV_LIBS}
        ${JNI_LIBRARIES}

        # 使用-Wl,前缀将选项直接传递给链接器，来静态链接pthread
        "-Wl,-Bstatic"
        -lpthread          # 请求链接pthread库，此时链接器会寻找libpthread.a
        "-Wl,-Bdynamic"

        # 链接其他必要的Windows系统库
        ws2_32 wsock32 secur32 crypt32 iphlpapi bcrypt
    )
endif()

# ----------------- 4. 输出和安装 -----------------
set_target_properties(dataset_export PROPERTIES
    PREFIX ""
    OUTPUT_NAME "dataset_export"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/../lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/../lib"
)

if(EXISTS "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native")
    add_custom_command(TARGET dataset_export POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dataset_export>
        "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native/"
        COMMENT "Copying library to Java resources directory"
    )
endif()

# ... (您现有的代码)

# ==============================================================================
# --- 5. 自动复制依赖项 (Pthread + OpenCV) ---
# ==============================================================================
if(MINGW)
    # --- 准备工作 ---
    # 定义构建后要执行的命令列表
    set(POST_BUILD_COMMANDS "")
    # 定义需要复制的OpenCV组件
    set(OPENCV_COMPONENTS core imgproc imgcodecs)

    # --- 步骤 A: 定位并准备复制 Pthread DLL ---
    get_filename_component(MINGW_BIN_DIR ${CMAKE_CXX_COMPILER} DIRECTORY)
    set(PTHREAD_DLL "${MINGW_BIN_DIR}/libwinpthread-1.dll")
    if(EXISTS "${PTHREAD_DLL}")
        message(STATUS "✓ Found Pthread DLL to copy: ${PTHREAD_DLL}")
        list(APPEND POST_BUILD_COMMANDS
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${PTHREAD_DLL}" $<TARGET_FILE_DIR:dataset_export>
            COMMENT "Copying libwinpthread-1.dll to build output..."
        )
    else()
        message(WARNING "Could not find pthread DLL: ${PTHREAD_DLL}")
    endif()


    # --- 步骤 B: 定位并准备复制 OpenCV DLLs ---
    # 从 OpenCV_DIR (e.g., .../lib/cmake/opencv4) 推断出安装根目录
    get_filename_component(OPENCV_CMAKE_DIR ${OpenCV_DIR} DIRECTORY)
    get_filename_component(OPENCV_LIB_DIR ${OPENCV_CMAKE_DIR} DIRECTORY)
    get_filename_component(OPENCV_INSTALL_PREFIX ${OPENCV_LIB_DIR} DIRECTORY)
    set(OPENCV_BIN_DIR "${OPENCV_INSTALL_PREFIX}/bin")
    message(STATUS "✓ Found OpenCV binaries directory: ${OPENCV_BIN_DIR}")
    
    # 构造OpenCV版本后缀，例如 4.12.0 -> 412
    set(OPENCV_DLL_VERSION_SUFFIX "${OpenCV_VERSION_MAJOR}${OpenCV_VERSION_MINOR}")

    foreach(COMPONENT ${OPENCV_COMPONENTS})
        set(OPENCV_DLL_NAME "libopencv_${COMPONENT}-${OPENCV_DLL_VERSION_SUFFIX}.dll")
        set(OPENCV_DLL_PATH "${OPENCV_BIN_DIR}/${OPENCV_DLL_NAME}")

        if(EXISTS "${OPENCV_DLL_PATH}")
            message(STATUS "✓ Found OpenCV DLL to copy: ${OPENCV_DLL_PATH}")
            list(APPEND POST_BUILD_COMMANDS
                COMMAND ${CMAKE_COMMAND} -E copy_if_different "${OPENCV_DLL_PATH}" $<TARGET_FILE_DIR:dataset_export>
                COMMENT "Copying ${OPENCV_DLL_NAME} to build output..."
            )
        else()
            message(WARNING "Could not find OpenCV component DLL: ${OPENCV_DLL_PATH}")
        endif()
    endforeach()


    # --- 步骤 C: 配置到Java资源目录的复制 ---
    set(JAVA_NATIVE_DIR "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native")
    if(EXISTS "${JAVA_NATIVE_DIR}")
        message(STATUS "✓ Will copy all artifacts to Java resources: ${JAVA_NATIVE_DIR}")
        
        # 1. 复制我们自己的库
        list(APPEND POST_BUILD_COMMANDS
            COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dataset_export> "${JAVA_NATIVE_DIR}/"
            COMMENT "Copying main library to Java resources..."
        )
        # 2. 复制 Pthread
        if(EXISTS "${PTHREAD_DLL}")
            list(APPEND POST_BUILD_COMMANDS
                COMMAND ${CMAKE_COMMAND} -E copy_if_different "${PTHREAD_DLL}" "${JAVA_NATIVE_DIR}/"
                COMMENT "Copying pthread DLL to Java resources..."
            )
        endif()
        # 3. 复制 OpenCV DLLs
        foreach(COMPONENT ${OPENCV_COMPONENTS})
             set(OPENCV_DLL_NAME "libopencv_${COMPONENT}-${OPENCV_DLL_VERSION_SUFFIX}.dll")
             set(OPENCV_DLL_PATH "${OPENCV_BIN_DIR}/${OPENCV_DLL_NAME}")
             if(EXISTS "${OPENCV_DLL_PATH}")
                list(APPEND POST_BUILD_COMMANDS
                    COMMAND ${CMAKE_COMMAND} -E copy_if_different "${OPENCV_DLL_PATH}" "${JAVA_NATIVE_DIR}/"
                    COMMENT "Copying ${OPENCV_DLL_NAME} to Java resources..."
                )
             endif()
        endforeach()
    endif()

    # --- 步骤 D: 将所有准备好的命令一次性添加到构建目标 ---
    if(POST_BUILD_COMMANDS)
        add_custom_command(TARGET dataset_export POST_BUILD ${POST_BUILD_COMMANDS})
    endif()

endif()

message(STATUS "--- Build configuration finished ---")

