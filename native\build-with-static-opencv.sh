#!/bin/bash

echo "========================================"
echo "开始构建项目 (动态OpenCV, 静态运行时)"
echo "========================================"

# 1. 清理环境
echo "--- 清理旧的构建文件 ---"
rm -rf build lib
mkdir build

# 2. 进入构建目录
cd build

# 3. 运行CMake配置
echo ""
echo "--- 运行CMake ---"
# 使用Release模式构建，并指定生成器
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败！"
    exit 1
fi

# 4. 编译
echo ""
echo "--- 开始编译 (使用 $(nproc) 个核心) ---"
cmake --build . -- -j$(nproc)
if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    exit 1
fi

# 5. 返回根目录
cd ..

echo ""
echo "========================================"
echo "✅ 构建成功！"
echo "========================================"

# 6. 运行依赖检查
echo ""
echo "--- 自动运行依赖检查脚本 ---"
./check-dependencies.sh
